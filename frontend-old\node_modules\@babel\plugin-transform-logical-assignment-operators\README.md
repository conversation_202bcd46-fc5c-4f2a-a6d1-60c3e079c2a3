# @babel/plugin-transform-logical-assignment-operators

> Transforms logical assignment operators into short-circuited assignments

See our website [@babel/plugin-transform-logical-assignment-operators](https://babeljs.io/docs/babel-plugin-transform-logical-assignment-operators) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-logical-assignment-operators
```

or using yarn:

```sh
yarn add @babel/plugin-transform-logical-assignment-operators --dev
```
